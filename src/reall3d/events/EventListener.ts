// ==============================================
// Copyright (c) 2025 reall3d.com, MIT license
// ==============================================
import { Matrix4, PerspectiveCamera, Vector3, Scene, Quaternion } from 'three';
import { Events } from './Events';
import {
    GetCanvas,
    EventListenerDispose,
    StopAutoRotate,
    GetOptions,
    StartAutoRotate,
    GetRenderer,
    GetCamera,
    KeyActionCheckAndExecute,
    SplatMeshSwitchDisplayMode,
    SplatUpdatePointMode,
    RotateAt,
    RotateLeft,
    RotateRight,
    RaycasterRayIntersectPoints,
    CameraSetLookAt,
    SelectPointAndLookAt,
    ControlPlaneSwitchVisible,
    GetCanvasSize,
    GetControls,
    ViewerNeedUpdate,
    ControlsUpdateRotateAxis,
    GetScene,
    SplatSetPointcloudMode,
    SplatSwitchDisplayMode,
    IsControlPlaneVisible,
    SplatUpdateLightFactor,
    SelectMarkPoint,
    SplatUpdateMarkPoint,
    ClearMarkPoint,
    GetCSS3DRenderer,
    RaycasterRayDistanceToPoint,
    MarkUpdateVisible,
    MetaSaveSmallSceneCameraInfo,
    MarkFinish,
    CancelCurrentMark,
    Flying,
    FlyDisable,
    AddFlyPosition,
    ClearFlyPosition,
    PrintInfo,
    GetSplatMesh,
    OnViewerUpdate,
    FocusAabbCenter,
    GetAabbCenter,
    FlySavePositions,
    StopBgAudio,
    CameraMoveForward,
    CameraMoveBackward,
    CameraMoveLeft,
    CameraMoveRight,
    CameraMoveUp,
    CameraMoveDown,
    CameraRotateUpFPS,
    CameraRotateDownFPS,
    CameraRotateLeftFPS,
    CameraRotateRightFPS,
} from './EventConstants';
import { Reall3dViewerOptions } from '../viewer/Reall3dViewerOptions';
import { SplatMesh } from '../meshs/splatmesh/SplatMesh';
import { CameraControls } from '../controls/CameraControls';
import { CSS3DRenderer } from 'three/addons/renderers/CSS3DRenderer.js';
import { MarkDistanceLine } from '../meshs/mark/MarkDistanceLine';
import { MarkMultiLines } from '../meshs/mark/MarkMultiLines';
import { MarkSinglePoint } from '../meshs/mark/MarkSinglePoint';
import { MarkMultiPlans } from '../meshs/mark/MarkMulitPlans';
import { MarkCirclePlan } from '../meshs/mark/MarkCirclePlan';
import { globalEv } from './GlobalEV';

class MouseState {
    public down: number = 0;
    public move: boolean = false;
    public downTime: number = 0;
    public isDbClick: boolean = false;
    public x: number = 0;
    public y: number = 0;
    public lastClickX: number = 0;
    public lastClickY: number = 0;
    public lastClickPointTime: number = 0;
    public lastMovePoint: Vector3 = null;
    public lastMovePointTime: number = 0;

    // 触摸相关状态
    public touchStartDistance: number = 0; // 两指初始距离
    public touchPrevDistance: number = 0; // 两指上一帧距离
    public touchStartX1: number = 0; // 第一个触摸点初始X坐标
    public touchStartY1: number = 0; // 第一个触摸点初始Y坐标
    public touchStartX2: number = 0; // 第二个触摸点初始X坐标
    public touchStartY2: number = 0; // 第二个触摸点初始Y坐标
    public touchPrevX1: number = 0; // 第一个触摸点上一帧X坐标
    public touchPrevY1: number = 0; // 第一个触摸点上一帧Y坐标
    public touchPrevX2: number = 0; // 第二个触摸点上一帧X坐标
    public touchPrevY2: number = 0; // 第二个触摸点上一帧Y坐标
}

export function setupEventListener(events: Events) {
    const on = (key: number, fn?: Function, multiFn?: boolean): Function | Function[] => events.on(key, fn, multiFn);
    const fire = (key: number, ...args: any): any => events.fire(key, ...args);
    const canvas: HTMLCanvasElement = fire(GetCanvas);
    let keySet: Set<string> = new Set();

    let disposed: boolean;
    let mouseState: MouseState = new MouseState();
    let lastActionTome: number;

    // 第一人称视角角度状态
    let fpsPitch = 0; // 俯仰角（上下看）
    let fpsYaw = 0; // 偏航角（左右看）

    // 初始化第一人称视角的欧拉角
    function initFPSEulerAngles() {
        const camera = fire(GetCamera);
        const direction = new Vector3();
        camera.getWorldDirection(direction);

        // 根据当前相机朝向计算初始欧拉角
        fpsPitch = Math.asin(direction.y);
        fpsYaw = Math.atan2(direction.z, direction.x);
    }

    on(StartAutoRotate, () => {
        const controls = fire(GetControls);
        const options = fire(GetOptions);
        controls.autoRotate = options.autoRotate = true;
        console.log('[StartAutoRotate] 自动旋转已启动, controls.autoRotate:', controls.autoRotate, 'options.autoRotate:', options.autoRotate);
    });
    on(StopAutoRotate, (flyDisable: boolean = true) => {
        const controls = fire(GetControls);
        const options = fire(GetOptions);
        controls.autoRotate = options.autoRotate = false;
        console.log('[StopAutoRotate] 自动旋转已停止, controls.autoRotate:', controls.autoRotate, 'options.autoRotate:', options.autoRotate);
        flyDisable && fire(FlyDisable);
    });

    on(RotateAt, (rotateLeft: boolean = true) => {
        if (disposed) return;
        const controls: CameraControls = fire(GetControls);
        const angle = rotateLeft ? Math.PI / 128 : -(Math.PI / 128);
        const matrix4: Matrix4 = new Matrix4().makeRotationAxis(new Vector3(0, 0, -1).transformDirection(controls.object.matrixWorld), angle);
        controls.object.up.transformDirection(matrix4);
        fire(ViewerNeedUpdate);
    });

    on(RotateLeft, () => {
        if (disposed) return;
        const opts: Reall3dViewerOptions = fire(GetOptions);
        const useCustomControl = opts.useCustomControl === true;
        if (useCustomControl) {
            const camera = fire(GetCamera);
            const scene: Scene = fire(GetScene);

            // 获取模型的位置
            let splat: SplatMesh;
            scene.traverse((obj: any) => {
                if (obj instanceof SplatMesh) {
                    splat = obj; // 这是需要旋转的模型
                }
            });

            if (splat) {
                // 获取相机指向模型的向量（旋转轴）
                const modelPosition = splat.position.clone();
                const cameraPosition = camera.position.clone();
                const axis = modelPosition.sub(cameraPosition).normalize();

                // 创建四元数旋转
                const angle = -(Math.PI / 128); // 注意：角度取负值
                const quaternion = new Quaternion().setFromAxisAngle(axis, angle);

                // 应用四元数旋转到模型上
                splat.applyQuaternion(quaternion);
            }

            fire(ViewerNeedUpdate);
        } else {
            events.fire(RotateAt, true);
        }
    });

    on(RotateRight, () => {
        if (disposed) return;
        const opts: Reall3dViewerOptions = fire(GetOptions);
        const useCustomControl = opts.useCustomControl === true;
        if (useCustomControl) {
            const camera = fire(GetCamera);
            const scene: Scene = fire(GetScene);

            // 获取模型的位置
            let splat: SplatMesh;
            scene.traverse((obj: any) => {
                if (obj instanceof SplatMesh) {
                    splat = obj; // 这是需要旋转的模型
                }
            });

            if (splat) {
                // 获取相机指向模型的向量（旋转轴）
                const modelPosition = splat.position.clone();
                const cameraPosition = camera.position.clone();
                const axis = modelPosition.sub(cameraPosition).normalize();

                // 创建四元数旋转
                const angle = Math.PI / 128; // 注意：角度取正值
                const quaternion = new Quaternion().setFromAxisAngle(axis, angle);

                // 应用四元数旋转到模型上
                splat.applyQuaternion(quaternion);
            }

            fire(ViewerNeedUpdate);
        } else {
            events.fire(RotateAt, false);
        }
    });

    on(SplatSetPointcloudMode, (isPointcloudMode?: boolean) => {
        const opts: Reall3dViewerOptions = fire(GetOptions);
        isPointcloudMode ??= !opts.pointcloudMode;
        opts.pointcloudMode = isPointcloudMode;
        const scene: Scene = fire(GetScene);
        scene.traverse((obj: any) => obj instanceof SplatMesh && (obj as SplatMesh).fire(SplatUpdatePointMode, isPointcloudMode));
    });
    on(SplatSwitchDisplayMode, () => {
        const scene: Scene = fire(GetScene);
        scene.traverse((obj: any) => obj instanceof SplatMesh && (obj as SplatMesh).fire(SplatMeshSwitchDisplayMode));
    });
    on(SplatUpdateLightFactor, (lightFactor: number) => {
        const scene: Scene = fire(GetScene);
        scene.traverse((obj: any) => obj instanceof SplatMesh && (obj as SplatMesh).fire(SplatUpdateLightFactor, lightFactor));
    });
    on(FocusAabbCenter, () => {
        const scene: Scene = fire(GetScene);
        let splat: SplatMesh;
        scene.traverse((obj: any) => obj instanceof SplatMesh && (splat = obj));
        splat && fire(CameraSetLookAt, splat.fire(GetAabbCenter));
    });

    on(CameraMoveForward, (speed: number = undefined) => {
        if (disposed) return;
        const camera = fire(GetCamera);
        const controls = fire(GetControls);
        const opts: Reall3dViewerOptions = fire(GetOptions);

        // 判断是否在第一人称模式
        const useCustomControl = opts.useCustomControl === true;

        // 优先使用传入的参数，其次使用选项中的值，最后使用默认值
        const moveSpeed = speed !== undefined ? speed : opts.cameraMoveSpeed !== undefined ? opts.cameraMoveSpeed : 0.005;

        const direction = new Vector3();
        camera.getWorldDirection(direction);

        // 移动相机
        camera.position.addScaledVector(direction, moveSpeed);

        if (useCustomControl) {
            // 在FPS模式下，保持相对位置不变
            const targetPoint = new Vector3().copy(camera.position).add(direction);
            controls.target.copy(targetPoint);
        } else {
            // 非FPS模式，正常移动目标点
            controls.target.addScaledVector(direction, moveSpeed);
        }

        fire(ViewerNeedUpdate);
    });

    on(CameraMoveBackward, (speed: number = undefined) => {
        if (disposed) return;
        const camera = fire(GetCamera);
        const controls = fire(GetControls);
        const opts: Reall3dViewerOptions = fire(GetOptions);

        // 优先使用传入的参数，其次使用选项中的值，最后使用默认值
        const moveSpeed = speed !== undefined ? speed : opts.cameraMoveSpeed !== undefined ? opts.cameraMoveSpeed : 0.005;

        const direction = new Vector3();
        camera.getWorldDirection(direction);

        camera.position.addScaledVector(direction, -moveSpeed);
        controls.target.addScaledVector(direction, -moveSpeed);

        fire(ViewerNeedUpdate);
    });

    on(CameraMoveLeft, (speed: number = undefined) => {
        if (disposed) return;
        const camera = fire(GetCamera);
        const controls = fire(GetControls);
        const opts: Reall3dViewerOptions = fire(GetOptions);

        const moveSpeed = speed !== undefined ? speed : opts.cameraMoveSpeed !== undefined ? opts.cameraMoveSpeed : 0.005;

        const right = new Vector3();
        // 获取相机的up向量和前方向向量，通过叉乘得到右向量
        const up = new Vector3(0, 1, 0);
        const forward = new Vector3();
        camera.getWorldDirection(forward);
        right.crossVectors(up, forward).normalize();

        // 向左移动就是向右向量的反方向移动
        camera.position.addScaledVector(right, -moveSpeed);
        controls.target.addScaledVector(right, -moveSpeed);

        fire(ViewerNeedUpdate);
    });

    on(CameraMoveRight, (speed: number = undefined) => {
        if (disposed) return;
        const camera = fire(GetCamera);
        const controls = fire(GetControls);
        const opts: Reall3dViewerOptions = fire(GetOptions);

        const moveSpeed = speed !== undefined ? speed : opts.cameraMoveSpeed !== undefined ? opts.cameraMoveSpeed : 0.005;

        const right = new Vector3();
        // 获取相机的up向量和前方向向量，通过叉乘得到右向量
        const up = new Vector3(0, 1, 0);
        const forward = new Vector3();
        camera.getWorldDirection(forward);
        right.crossVectors(up, forward).normalize();

        camera.position.addScaledVector(right, moveSpeed);
        controls.target.addScaledVector(right, moveSpeed);

        fire(ViewerNeedUpdate);
    });

    on(CameraMoveUp, (speed: number = undefined) => {
        if (disposed) return;
        const camera = fire(GetCamera);
        const controls = fire(GetControls);
        const opts: Reall3dViewerOptions = fire(GetOptions);

        // 判断是否在第一人称模式
        const useCustomControl = opts.useCustomControl === true;

        const moveSpeed = speed !== undefined ? speed : opts.cameraMoveSpeed !== undefined ? opts.cameraMoveSpeed : 0.005;

        if (useCustomControl) {
            // 在第一人称模式下，我们使用当前屏幕的"上"方向，这与相机的本地y轴相对应
            const upDirection = new Vector3(0, 1, 0).applyQuaternion(camera.quaternion);
            console.log('[CameraMoveUp] 第一人称模式下使用屏幕上方向:', upDirection.toArray());

            // 移动相机
            camera.position.addScaledVector(upDirection, moveSpeed);

            // 同时移动目标点，保持相机和target的相对位置不变
            controls.target.addScaledVector(upDirection, moveSpeed);
        } else {
            // 非FPS模式下，使用世界坐标系的上方向
            // 世界坐标系的上方向，这个项目是反的。。。
            const up = new Vector3(0, 1, 0);

            // 向上移动
            camera.position.addScaledVector(up, -moveSpeed);
            controls.target.addScaledVector(up, -moveSpeed);
        }

        fire(ViewerNeedUpdate);
    });

    on(CameraMoveDown, (speed: number = undefined) => {
        if (disposed) return;
        const camera = fire(GetCamera);
        const controls = fire(GetControls);
        const opts: Reall3dViewerOptions = fire(GetOptions);

        // 判断是否在第一人称模式
        const useCustomControl = opts.useCustomControl === true;

        const moveSpeed = speed !== undefined ? speed : opts.cameraMoveSpeed !== undefined ? opts.cameraMoveSpeed : 0.005;

        if (useCustomControl) {
            // 在第一人称模式下，我们使用当前屏幕的"上"方向的反方向，这与相机的本地y轴的反方向相对应
            const upDirection = new Vector3(0, 1, 0).applyQuaternion(camera.quaternion);
            console.log('[CameraMoveDown] 第一人称模式下使用屏幕下方向:', upDirection.toArray());

            // 向下移动相机（与上方向相反）
            camera.position.addScaledVector(upDirection, -moveSpeed);

            // 同时移动目标点，保持相机和target的相对位置不变
            controls.target.addScaledVector(upDirection, -moveSpeed);
        } else {
            // 非FPS模式下，使用世界坐标系的上方向
            // 世界坐标系的上方向，这个项目是反的。。。
            const up = new Vector3(0, 1, 0);

            // 向下移动
            camera.position.addScaledVector(up, moveSpeed);
            controls.target.addScaledVector(up, moveSpeed);
        }

        fire(ViewerNeedUpdate);
    });

    // 第一人称视角 - 向上旋转（低头看）
    on(CameraRotateUpFPS, (angle: number = undefined) => {
        if (disposed) return;
        const camera = fire(GetCamera);
        const controls = fire(GetControls);

        // 保存当前相机位置
        const originalPosition = camera.position.clone();

        // 更新俯仰角（限制范围，防止过度旋转）
        const rotateAngle = angle !== undefined ? angle : 0.05;
        fpsPitch += rotateAngle;
        fpsPitch = Math.min(Math.PI / 2 - 0.1, Math.max(-Math.PI / 2 + 0.1, fpsPitch)); // 限制在±90度范围内

        // 基于欧拉角计算新的前方向
        const direction = new Vector3(Math.cos(fpsPitch) * Math.cos(fpsYaw), Math.sin(fpsPitch), Math.cos(fpsPitch) * Math.sin(fpsYaw)).normalize();

        // 设置目标点，但保持相机位置不变
        const targetPoint = new Vector3().copy(originalPosition).add(direction);
        controls.target.copy(targetPoint);
        camera.lookAt(targetPoint);

        // 强制恢复原始位置，确保位置不变
        camera.position.copy(originalPosition);

        fire(ViewerNeedUpdate);
    });

    // 第一人称视角 - 向下旋转（抬头看）
    on(CameraRotateDownFPS, (angle: number = undefined) => {
        if (disposed) return;
        const camera = fire(GetCamera);
        const controls = fire(GetControls);

        // 保存当前相机位置
        const originalPosition = camera.position.clone();

        // 更新俯仰角（限制范围，防止过度旋转）
        const rotateAngle = angle !== undefined ? angle : 0.05;
        fpsPitch -= rotateAngle;
        fpsPitch = Math.min(Math.PI / 2 - 0.1, Math.max(-Math.PI / 2 + 0.1, fpsPitch)); // 限制在±90度范围内

        // 基于欧拉角计算新的前方向
        const direction = new Vector3(Math.cos(fpsPitch) * Math.cos(fpsYaw), Math.sin(fpsPitch), Math.cos(fpsPitch) * Math.sin(fpsYaw)).normalize();

        // 设置目标点，但保持相机位置不变
        const targetPoint = new Vector3().copy(originalPosition).add(direction);
        controls.target.copy(targetPoint);
        camera.lookAt(targetPoint);

        // 强制恢复原始位置，确保位置不变
        camera.position.copy(originalPosition);

        fire(ViewerNeedUpdate);
    });

    // 第一人称视角 - 向左旋转
    on(CameraRotateLeftFPS, (angle: number = undefined) => {
        if (disposed) return;
        const camera = fire(GetCamera);
        const controls = fire(GetControls);

        // 保存当前相机位置
        const originalPosition = camera.position.clone();
        console.log('[CameraRotateLeftFPS] 旋转前相机位置:', originalPosition.toArray());

        // 更新偏航角
        const rotateAngle = angle !== undefined ? angle : 0.05;
        fpsYaw += rotateAngle;

        // 基于欧拉角计算新的前方向
        const direction = new Vector3(Math.cos(fpsPitch) * Math.cos(fpsYaw), Math.sin(fpsPitch), Math.cos(fpsPitch) * Math.sin(fpsYaw)).normalize();

        // 设置目标点，但保持相机位置不变
        const targetPoint = new Vector3().copy(originalPosition).add(direction);
        controls.target.copy(targetPoint);
        camera.lookAt(targetPoint);

        // 强制恢复原始位置，确保位置不变
        camera.position.copy(originalPosition);
        console.log('[CameraRotateLeftFPS] 旋转后相机位置:', camera.position.toArray());

        // 确保OrbitControls不会在帧更新时修改相机位置
        controls.minDistance = controls.maxDistance = controls.getDistance();

        fire(ViewerNeedUpdate);
    });

    // 第一人称视角 - 向右旋转
    on(CameraRotateRightFPS, (angle: number = undefined) => {
        if (disposed) return;
        const camera = fire(GetCamera);
        const controls = fire(GetControls);

        // 保存当前相机位置
        const originalPosition = camera.position.clone();

        // 更新偏航角
        const rotateAngle = angle !== undefined ? angle : 0.05;
        fpsYaw -= rotateAngle;

        // 基于欧拉角计算新的前方向
        const direction = new Vector3(Math.cos(fpsPitch) * Math.cos(fpsYaw), Math.sin(fpsPitch), Math.cos(fpsPitch) * Math.sin(fpsYaw)).normalize();

        // 设置目标点，但保持相机位置不变
        const targetPoint = new Vector3().copy(originalPosition).add(direction);
        controls.target.copy(targetPoint);
        camera.lookAt(targetPoint);

        // 强制恢复原始位置，确保位置不变
        camera.position.copy(originalPosition);

        // 确保OrbitControls不会在帧更新时修改相机位置
        controls.minDistance = controls.maxDistance = controls.getDistance();

        fire(ViewerNeedUpdate);
    });

    on(KeyActionCheckAndExecute, () => {
        if (!keySet.size) return;

        const opts: Reall3dViewerOptions = fire(GetOptions);
        if (!opts.enableKeyboard) return keySet.clear();

        if (opts.markMode && keySet.has('Escape')) {
            fire(CancelCurrentMark);
            keySet.clear();
            return;
        }

        // 检查是否启用了自定义控制
        const useCustomControl = opts.useCustomControl === true;

        // 检查是否按下了Shift键
        const isShiftPressed = keySet.has('Shift');

        // 处理Shift+WASD组合键 - 视角旋转
        if (isShiftPressed && useCustomControl) {
            // 旋转速度因子
            const rotateSpeed = 0.01; // 可以根据需要调整

            // Shift+W - 向上看(抬头)
            if (keySet.has('KeyW')) {
                fire(CameraRotateDownFPS, rotateSpeed);
            }

            // Shift+S - 向下看(低头)
            if (keySet.has('KeyS')) {
                fire(CameraRotateUpFPS, rotateSpeed);
            }

            // Shift+A - 向左看
            if (keySet.has('KeyA')) {
                fire(CameraRotateLeftFPS, rotateSpeed);
            }

            // Shift+D - 向右看
            if (keySet.has('KeyD')) {
                fire(CameraRotateRightFPS, rotateSpeed);
            }
        }
        // 只有在启用自定义控制时，才处理自定义移动，且不按Shift
        else if (useCustomControl && !isShiftPressed) {
            // 处理移动键，避免被其他条件清除keySet
            if (keySet.has('KeyW')) {
                fire(CameraMoveForward);
            }

            if (keySet.has('KeyS')) {
                fire(CameraMoveBackward);
            }

            if (keySet.has('KeyA')) {
                fire(CameraMoveLeft);
            }

            if (keySet.has('KeyD')) {
                fire(CameraMoveRight);
            }

            if (keySet.has('ArrowLeft')) {
                fire(CameraMoveLeft);
            }

            if (keySet.has('ArrowRight')) {
                fire(CameraMoveRight);
            }

            if (keySet.has('ArrowUp')) {
                fire(CameraMoveUp);
            }

            if (keySet.has('ArrowDown')) {
                fire(CameraMoveDown);
            }
        }

        // 然后处理其他只触发一次的键
        if (keySet.has('Space')) {
            opts.bigSceneMode ? fire(SplatSetPointcloudMode) : fire(SplatSwitchDisplayMode);
            keySet.clear();
        } else if (keySet.has('Escape')) {
            globalEv.fire(StopBgAudio);
            keySet.clear();
        } else if (keySet.has('KeyR')) {
            opts.autoRotate ? fire(StopAutoRotate) : fire(StartAutoRotate);
            keySet.clear();
        } else if (keySet.has('KeyM')) {
            fire(MarkUpdateVisible, !opts.markVisible);
            keySet.clear();
        } else if (useCustomControl && keySet.has('KeyQ')) {
            // 在自定义控制模式下，Q键实现横滚相机向左旋转
            fire(RotateLeft);
            // 不执行ControlPlaneSwitchVisible，与非自定义控制不同
            keySet.clear();
        } else if (useCustomControl && keySet.has('KeyE')) {
            // 在自定义控制模式下，E键实现横滚相机向右旋转
            fire(RotateRight);
            // 不执行ControlPlaneSwitchVisible，与非自定义控制不同
            keySet.clear();
        } else if (!useCustomControl && keySet.has('ArrowLeft')) {
            // 只在非useCustomControl模式下，左右方向键才用于旋转
            fire(RotateLeft);
            fire(ControlPlaneSwitchVisible, true);
            keySet.clear();
        } else if (!useCustomControl && keySet.has('ArrowRight')) {
            // 只在非useCustomControl模式下，左右方向键才用于旋转
            fire(RotateRight);
            fire(ControlPlaneSwitchVisible, true);
            keySet.clear();
        } else if (keySet.has('KeyP')) {
            fire(Flying, true);
            keySet.clear();
        } else if (keySet.has('Equal')) {
            fire(AddFlyPosition);
            keySet.clear();
        } else if (keySet.has('Minus')) {
            fire(ClearFlyPosition);
            keySet.clear();
        } else if (keySet.has('KeyY')) {
            fire(FlySavePositions, false);
            fire(MetaSaveSmallSceneCameraInfo);
            keySet.clear();
        } else if (keySet.has('KeyI')) {
            fire(PrintInfo);
            keySet.clear();
        } else if (keySet.has('KeyF')) {
            fire(FocusAabbCenter);
            keySet.clear();
        } else if (keySet.has('F2')) {
            !opts.bigSceneMode && window.open('/editor/index.html?url=' + encodeURIComponent((fire(GetSplatMesh) as SplatMesh).meta.url));
            keySet.clear();
        } else if (keySet.has('KeyW')) {
            moveForward(fire(GetControls), 0.15);
            keySet.clear();
        } else if (keySet.has('KeyS')) {
            moveBackward(fire(GetControls), 0.15);
            keySet.clear();
        } else if (keySet.has('KeyA')) {
            moveLeft(fire(GetControls), 0.15);
            keySet.clear();
        } else if (keySet.has('KeyD')) {
            moveRight(fire(GetControls), 0.15);
            keySet.clear();
        } else if (keySet.has('KeyQ')) {
            rotateTargetLeft(fire(GetControls));
            keySet.clear();
        } else if (keySet.has('KeyE')) {
            rotateTargetRight(fire(GetControls));
            keySet.clear();
        } else if (keySet.has('KeyC')) {
            rotateTargetUp(fire(GetControls));
            keySet.clear();
        } else if (keySet.has('KeyZ')) {
            rotateTargetDown(fire(GetControls));
            keySet.clear();
        }
    });

    on(SelectPointAndLookAt, async (x: number, y: number) => {
        if (mouseState.move) return; // 鼠标有移动时忽略

        // 在第一人称模式下不改变相机目标点
        const opts: Reall3dViewerOptions = fire(GetOptions);
        const useCustomControl = opts.useCustomControl === true;
        if (useCustomControl) {
            console.log('[SelectPointAndLookAt] 第一人称模式下忽略点击选择');
            return;
        }

        const rs: Vector3[] = await fire(RaycasterRayIntersectPoints, x, y);
        if (rs.length) {
            console.log('[SelectPointAndLookAt] 设置相机目标点:', rs[0].toArray());
            fire(CameraSetLookAt, rs[0], true, false); // 最后参数false时平移效果，true时旋转效果
        }
    });

    on(SelectMarkPoint, async (x: number, y: number) => {
        const scene: Scene = fire(GetScene);
        const splats: SplatMesh[] = [];
        scene.traverse(child => child instanceof SplatMesh && splats.push(child));

        const rs: Vector3[] = await fire(RaycasterRayIntersectPoints, x, y);
        if (rs.length) {
            splats.length && splats[0].fire(SplatUpdateMarkPoint, rs[0].x, rs[0].y, rs[0].z, true);
            return new Vector3(rs[0].x, rs[0].y, rs[0].z);
        } else {
            splats.length && splats[0].fire(SplatUpdateMarkPoint, 0, 0, 0, false);
        }
        return null;
    });

    on(ClearMarkPoint, async () => {
        const scene: Scene = fire(GetScene);
        const splats: SplatMesh[] = [];
        scene.traverse(child => child instanceof SplatMesh && splats.push(child));
        for (let i = 0; i < splats.length; i++) {
            splats[i].fire(SplatUpdateMarkPoint, 0, 0, 0, false);
        }
    });

    const keydownEventListener = (e: KeyboardEvent) => {
        if (e.target['type'] === 'text') return;

        if (disposed || e.code === 'F5') return;
        e.preventDefault();
        if (e.code !== 'KeyR') {
            // 添加按键到keySet，包括修饰键状态
            keySet.add(e.code);
            // 记录Shift键状态
            if (e.code === 'ShiftLeft' || e.code === 'ShiftRight') {
                keySet.add('Shift');
            }
            fire(StopAutoRotate);
        }
        lastActionTome = Date.now();
    };

    const keyupEventListener = (e: KeyboardEvent) => {
        if (e.target['type'] === 'text') return;

        if (disposed) return;
        e.code === 'KeyR' && keySet.add(e.code);
        if (e.code === 'ArrowLeft' || e.code === 'ArrowRight') {
            fire(ControlsUpdateRotateAxis);
        }

        // 从keySet中移除按键避免不会继续触发移动
        keySet.delete(e.code);
        // 处理Shift键释放
        if (e.code === 'ShiftLeft' || e.code === 'ShiftRight') {
            keySet.delete('Shift');
        }

        lastActionTome = Date.now();
    };

    on(
        OnViewerUpdate,
        () => {
            if (fire(IsControlPlaneVisible) && Date.now() - lastActionTome > 2000) {
                fire(ControlPlaneSwitchVisible, false);
                // 旋转轴调整后 2 秒内无操作，自动保存相机位置
                fire(MetaSaveSmallSceneCameraInfo);
            }

            // 如果配置了自动旋转，且用户停止交互超过3秒，则重新启动自动旋转
            const opts: Reall3dViewerOptions = fire(GetOptions);
            const controls = fire(GetControls);
            if (opts.autoRotate && !controls.autoRotate && Date.now() - lastActionTome > 3000) {
                // 只有在非自定义控制模式下才重新启动自动旋转
                if (!opts.useCustomControl) {
                    console.log('[AutoRotate] 用户停止交互超过3秒，重新启动自动旋转');
                    fire(StartAutoRotate);
                }
            }
        },
        true,
    );

    const blurEventListener = () => {
        keySet.clear();
    };

    const wheelEventListener = (e: WheelEvent) => {
        parent && setTimeout(() => window.focus());
        e.preventDefault();
        if (disposed) return;
        fire(StopAutoRotate);

        // 获取全局选项，检查是否启用了自定义控制
        const opts: Reall3dViewerOptions = fire(GetOptions);
        const useCustomControl = opts.useCustomControl === true;

        // 在useCustomControl模式下，手动实现滚轮缩放功能
        if (useCustomControl) {
            const camera = fire(GetCamera) as PerspectiveCamera;
            const controls = fire(GetControls) as CameraControls;

            // 计算缩放系数（调整为更平滑的缩放速度）
            const zoomScale = e.deltaY > 0 ? 1.1 : 0.9; // 向下滚动放大，向上滚动缩小
            console.log('[Wheel] useCustomControl模式下缩放系数:', zoomScale);

            // 获取当前相机到目标的方向向量
            const direction = camera.position.clone().sub(controls.target).normalize();

            // 计算当前距离
            const currentDistance = camera.position.distanceTo(controls.target);

            // 计算新的距离，并限制在合理范围内
            const newDistance = Math.max(0.1, Math.min(1000, currentDistance * zoomScale));

            // 计算新的相机位置
            const newPosition = controls.target.clone().add(direction.multiplyScalar(newDistance));

            // 更新相机位置
            camera.position.copy(newPosition);

            // 强制更新场景
            fire(ViewerNeedUpdate);

            console.log('[Wheel] useCustomControl模式下缩放，距离:', newDistance.toFixed(2));
        }

        // 在自定义控制模式下，滚轮控制FOV而不是缩放
        // if (useCustomControl) {
        //     // 禁用默认的OrbitControls缩放行为
        //     const controls = fire(GetControls);
        //     controls.enabled = false;

        //     // 获取相机并计算FOV变化
        //     const camera = fire(GetCamera) as PerspectiveCamera;
        //     const deltaFov = e.deltaY > 0 ? 1 : -1; // 向下滚动增大FOV，向上滚动减小FOV

        //     // 限制FOV范围在10到120度之间
        //     const newFov = Math.max(10, Math.min(120, camera.fov + deltaFov));
        //     camera.fov = newFov;
        //     camera.updateProjectionMatrix();

        //     // 强制更新场景
        //     fire(ViewerNeedUpdate);
        //     console.log('[Wheel] 调整FOV:', camera.fov);
        // }

        lastActionTome = Date.now();
    };

    const canvasContextmenuEventListener = (e: MouseEvent) => {
        e.preventDefault();
        if (disposed) return;
        fire(StopAutoRotate);
        lastActionTome = Date.now();
    };

    let markDistanceLine: MarkDistanceLine;
    let markMultiLines: MarkMultiLines;
    let markMultiPlans: MarkMultiPlans;
    let markCirclePlan: MarkCirclePlan;

    on(CancelCurrentMark, () => {
        markDistanceLine?.dispose();
        markMultiLines?.dispose();
        markMultiPlans?.dispose();
        markCirclePlan?.dispose();
        markDistanceLine = null;
        markMultiLines = null;
        markMultiPlans = null;
        markCirclePlan = null;
        mouseState.lastMovePoint = null;
        fire(MarkFinish);
    });

    const canvasMousedownEventListener = async (e: MouseEvent) => {
        parent && setTimeout(() => window.focus());
        e.preventDefault();
        if (disposed) return;
        fire(StopAutoRotate);

        // 修改鼠标按键识别逻辑，添加对中键的识别（button=1）
        if (e.button === 1) {
            mouseState.down = 3; // 中键使用3表示
        } else {
            mouseState.down = e.button === 2 ? 2 : 1;
        }
        mouseState.move = false;
        mouseState.isDbClick = Date.now() - mouseState.downTime < 300;

        // 记录鼠标按下的位置，用于后续计算移动距离
        mouseState.x = e.clientX;
        mouseState.y = e.clientY;

        // 获取全局选项
        const opts: Reall3dViewerOptions = fire(GetOptions);
        const useCustomControl = opts.useCustomControl === true;

        // 在第一人称模式下，每次点击都确保控制器被禁用
        if (useCustomControl) {
            // 获取相机当前位置
            const camera = fire(GetCamera);
            const originalPosition = camera.position.clone();
            console.log('[MouseDown] FPS模式下相机位置:', originalPosition.toArray());

            // 立即禁用控制器
            const controls = fire(GetControls);
            controls.enabled = false;

            // 确保OrbitControls不会在帧更新时修改相机位置
            controls.minDistance = controls.maxDistance = controls.getDistance();

            // 重新应用第一人称模式设置
            controls.updateControlMode(opts);

            console.log('[MouseDown] FPS模式下已禁用控制器，按钮:', mouseState.down);

            // 如果是左键点击，处理欧拉角初始化
            if (mouseState.down === 1) {
                console.log('[MouseDown] FPS模式下左键点击，位置:', e.clientX, e.clientY);

                // 只有在角度接近初始值时才初始化
                if (Math.abs(fpsPitch) < 0.001 && Math.abs(fpsYaw) < 0.001) {
                    console.log('[MouseDown] 初始化欧拉角');
                    initFPSEulerAngles();
                }

                // 确保相机位置不变
                camera.position.copy(originalPosition);

                // 阻止事件传播，避免其他处理器影响相机
                e.stopPropagation();
            }
        }

        lastActionTome = Date.now();
        mouseState.downTime = Date.now();
    };

    const canvasMousemoveEventListener = async (e: MouseEvent) => {
        e.preventDefault();
        if (disposed) return;

        // 获取全局选项
        const opts: Reall3dViewerOptions = fire(GetOptions);
        const useCustomControl = opts.useCustomControl === true;

        if (mouseState.down) {
            // 计算鼠标移动距离
            const deltaX = e.clientX - mouseState.x;
            const deltaY = e.clientY - mouseState.y;

            // 中键拖动实现相机前后移动，与上下键功能一致
            if (mouseState.down === 3) {
                // 阻止事件传播
                e.stopPropagation();

                // 显式禁用控制器，确保不发生任何自动行为
                const controls = fire(GetControls);
                controls.enabled = false;

                // 计算移动系数，根据鼠标移动距离调整移动速度
                const moveSpeed = opts.cameraMoveSpeed !== undefined ? opts.cameraMoveSpeed : 0.005;
                const moveFactor = 0.5; // 调整移动灵敏度

                // 垂直方向的移动控制前后移动
                if (Math.abs(deltaY) > 2) {
                    if (deltaY > 0) {
                        // 向后移动
                        fire(CameraMoveBackward, Math.abs(deltaY) * moveFactor * moveSpeed);
                    } else {
                        // 向前移动
                        fire(CameraMoveForward, Math.abs(deltaY) * moveFactor * moveSpeed);
                    }
                }

                // 更新鼠标位置用于下一帧计算
                mouseState.x = e.clientX;
                mouseState.y = e.clientY;

                // 强制更新场景
                fire(ViewerNeedUpdate);

                mouseState.move = true;
                lastActionTome = Date.now();
                return; // 直接返回，不执行后续代码
            }

            // 右键拖动实现相机平移，与方向键功能一致
            if (mouseState.down === 2 && useCustomControl) {
                // 阻止事件传播，禁用默认的OrbitControls行为
                e.stopPropagation();

                // 显式禁用控制器，确保不发生任何自动行为
                const controls = fire(GetControls);
                controls.enabled = false;

                // 计算移动系数，根据鼠标移动距离调整移动速度
                const moveSpeed = opts.cameraMoveSpeed !== undefined ? opts.cameraMoveSpeed : 0.005;
                const moveFactor = 0.5; // 调整移动灵敏度

                // 水平方向：左右移动
                if (Math.abs(deltaX) > 2) {
                    if (deltaX > 0) {
                        // 向左移动
                        fire(CameraMoveLeft, Math.abs(deltaX) * moveFactor * moveSpeed);
                    } else {
                        // 向右移动
                        fire(CameraMoveRight, Math.abs(deltaX) * moveFactor * moveSpeed);
                    }
                }

                // 垂直方向：上下移动
                if (Math.abs(deltaY) > 2) {
                    if (deltaY > 0) {
                        // 向上移动
                        fire(CameraMoveUp, Math.abs(deltaY) * moveFactor * moveSpeed);
                    } else {
                        // 向下移动
                        fire(CameraMoveDown, Math.abs(deltaY) * moveFactor * moveSpeed);
                    }
                }

                // 更新鼠标位置用于下一帧计算
                mouseState.x = e.clientX;
                mouseState.y = e.clientY;

                // 强制更新场景
                fire(ViewerNeedUpdate);

                mouseState.move = true;
                lastActionTome = Date.now();
                return; // 直接返回，不执行后续代码
            }

            // 如果启用了自定义控制和第一人称模式，且是左键拖动
            if (useCustomControl && mouseState.down === 1) {
                // 阻止事件传播，禁用默认的OrbitControls旋转行为
                e.stopPropagation();

                // 显式禁用控制器，确保不发生任何自动行为
                const controls = fire(GetControls);
                controls.enabled = false;

                // 保存相机当前位置，确保不会变化
                const camera = fire(GetCamera);
                const originalPosition = camera.position.clone();

                console.log('[MouseMove] FPS模式下左键拖动，移动距离:', deltaX, deltaY);

                // 调整灵敏度系数，防止转动过快
                const rotateFactorX = 0.001; // 水平旋转灵敏度
                const rotateFactorY = 0.001; // 垂直旋转灵敏度

                // 初始化欧拉角（如果是第一次进入FPS模式）
                if (Math.abs(fpsPitch) < 0.001 && Math.abs(fpsYaw) < 0.001) {
                    initFPSEulerAngles();
                }

                // 水平方向移动控制左右旋转
                if (Math.abs(deltaX) > 0) {
                    if (deltaX > 0) {
                        fire(CameraRotateRightFPS, Math.abs(deltaX) * rotateFactorX);
                    } else {
                        fire(CameraRotateLeftFPS, Math.abs(deltaX) * rotateFactorX);
                    }
                }

                // 垂直方向移动控制上下旋转
                if (Math.abs(deltaY) > 0) {
                    if (deltaY > 0) {
                        fire(CameraRotateUpFPS, Math.abs(deltaY) * rotateFactorY);
                    } else {
                        fire(CameraRotateDownFPS, Math.abs(deltaY) * rotateFactorY);
                    }
                }

                // 确保相机位置恢复到原始位置
                camera.position.copy(originalPosition);

                // 确保距离限制设置正确，防止OrbitControls自动调整距离
                controls.minDistance = controls.maxDistance = controls.getDistance();

                // 更新鼠标位置
                mouseState.x = e.clientX;
                mouseState.y = e.clientY;

                // 强制更新场景
                fire(ViewerNeedUpdate);

                // 仍然标记为移动状态，但阻止默认的拖拽行为
                mouseState.move = true;
                lastActionTome = Date.now();
                return; // 直接返回，不执行后续代码
            }

            mouseState.move = true;
            lastActionTome = Date.now();
        }

        // 标记处理 - 只在非FPS模式下处理
        if (!useCustomControl && opts.markMode) {
            const point: Vector3 = await fire(SelectMarkPoint, e.clientX, e.clientY); // 显示提示点
            if (point && !mouseState.down && opts.markType === 'distance' && markDistanceLine) {
                markDistanceLine.drawUpdate({ endPoint: point.toArray() });
            } else if (!mouseState.down && opts.markType === 'circle' && markCirclePlan) {
                if (point) {
                    markCirclePlan.drawUpdate(null, true, point);
                    mouseState.lastMovePoint = point;
                    mouseState.lastMovePointTime = Date.now();
                } else {
                    mouseState.lastMovePoint = null;
                    mouseState.lastMovePointTime = 0;
                }
            } else if (!mouseState.down && opts.markType === 'lines' && markMultiLines) {
                if (point) {
                    markMultiLines.drawUpdate(null, true, point);
                    mouseState.lastMovePoint = point;
                    mouseState.lastMovePointTime = Date.now();
                } else {
                    mouseState.lastMovePoint = null;
                    mouseState.lastMovePointTime = 0;
                }
            } else if (!mouseState.down && opts.markType === 'plans' && markMultiPlans) {
                if (point) {
                    markMultiPlans.drawUpdate(null, true, point);
                    mouseState.lastMovePoint = point;
                    mouseState.lastMovePointTime = Date.now();
                } else {
                    mouseState.lastMovePoint = null;
                    mouseState.lastMovePointTime = 0;
                }
            }
            // fire(ViewerNeedUpdate);
        }
    };

    const canvasMouseupEventListener = async (e: MouseEvent) => {
        e.preventDefault();
        if (disposed) return;
        const opts: Reall3dViewerOptions = fire(GetOptions);

        // 获取全局选项及判断是否在第一人称模式下
        const useCustomControl = opts.useCustomControl === true;

        // 如果在第一人称模式下，确保控制器正确设置
        if (useCustomControl) {
            // 获取当前相机和控制器
            const camera = fire(GetCamera);
            const controls = fire(GetControls);

            // 保存相机当前位置
            const originalPosition = camera.position.clone();

            // 记录鼠标松开时的信息
            console.log('[MouseUp] FPS模式下松开鼠标，按钮:', mouseState.down, '是否移动:', mouseState.move);

            // 重新更新控制器设置，确保禁用状态
            controls.enabled = false;
            controls.updateControlMode(opts);

            // 确保距离限制设置正确，防止自动调整距离
            controls.minDistance = controls.maxDistance = controls.getDistance();

            // 恢复相机位置，确保不变
            camera.position.copy(originalPosition);

            console.log('[MouseUp] FPS模式下相机位置:', camera.position.toArray());

            // 阻止事件传播
            e.stopPropagation();
        } else if (useCustomControl && mouseState.down === 2 && mouseState.move) {
            // 右键拖动后，恢复控制器状态
            const controls = fire(GetControls);
            controls.enabled = true;
            controls.update();

            // 阻止事件传播
            e.stopPropagation();
        } else if (mouseState.down === 3 && mouseState.move) {
            // 中键拖动后，恢复控制器状态
            const controls = fire(GetControls);
            controls.enabled = true;
            controls.update();

            // 阻止事件传播
            e.stopPropagation();
        }

        if (mouseState.isDbClick) {
            // 双击停止标注
            if (markMultiLines) {
                if (Math.abs(e.clientX - mouseState.lastClickX) < 2 && Math.abs(e.clientY - mouseState.lastClickY) < 2) {
                    // 两次双击的屏幕距离差小于2，则判定为停止标注的有效双击
                    markMultiLines.drawFinish(mouseState.lastClickPointTime > 0);
                    markMultiLines = null;
                    mouseState.lastMovePoint = null;
                }
            } else if (markMultiPlans) {
                if (Math.abs(e.clientX - mouseState.lastClickX) < 2 && Math.abs(e.clientY - mouseState.lastClickY) < 2) {
                    // 两次双击的屏幕距离差小于2，则判定为停止标注的有效双击
                    markMultiPlans.drawFinish(mouseState.lastClickPointTime > 0);
                    markMultiPlans = null;
                    mouseState.lastMovePoint = null;
                }
            }
        }

        if (opts.markMode) {
            if (mouseState.down === 1 && !mouseState.move && Date.now() - mouseState.downTime < 500) {
                if (opts.markType === 'point') {
                    const point: Vector3 = await fire(SelectMarkPoint, e.clientX, e.clientY);
                    if (point) {
                        const markSinglePoint = new MarkSinglePoint(events, await fire(SelectMarkPoint, e.clientX, e.clientY));
                        fire(GetScene).add(markSinglePoint);
                        markSinglePoint.drawFinish();
                    }
                } else if (opts.markType === 'distance') {
                    if (!markDistanceLine) {
                        // 开始测量
                        const point: Vector3 = await fire(SelectMarkPoint, e.clientX, e.clientY);
                        if (point) {
                            markDistanceLine = new MarkDistanceLine(events);
                            markDistanceLine.drawStart(point);
                            fire(GetScene).add(markDistanceLine);
                        }
                    } else {
                        // 完成测量
                        const point: Vector3 = await fire(SelectMarkPoint, e.clientX, e.clientY);
                        if (point) {
                            markDistanceLine.drawFinish(point);
                            markDistanceLine = null;
                        } else {
                            mouseState.isDbClick && fire(CancelCurrentMark); // 取消标记
                        }
                    }
                } else if (opts.markType === 'lines') {
                    if (!markMultiLines) {
                        // 开始
                        const point: Vector3 = await fire(SelectMarkPoint, e.clientX, e.clientY);
                        if (point) {
                            markMultiLines = new MarkMultiLines(events);
                            markMultiLines.drawStart(point);
                            fire(GetScene).add(markMultiLines);
                        }
                    } else {
                        // 继续
                        if (mouseState.lastMovePoint && fire(RaycasterRayDistanceToPoint, e.clientX, e.clientY, mouseState.lastMovePoint) < 0.03) {
                            // 点击位置与移动提示点相近，按选中提示点处理
                            markMultiLines.drawUpdate(null, true, mouseState.lastMovePoint, true);
                            mouseState.lastClickPointTime = Date.now();
                        } else {
                            // 按点击位置计算选点
                            const point: Vector3 = await fire(SelectMarkPoint, e.clientX, e.clientY);
                            if (point) {
                                markMultiLines.drawUpdate(null, true, point, true);
                                mouseState.lastClickPointTime = Date.now();
                            } else {
                                mouseState.lastClickPointTime = 0;
                            }
                        }
                    }
                } else if (opts.markType === 'plans') {
                    if (!markMultiPlans) {
                        // 开始
                        const point: Vector3 = await fire(SelectMarkPoint, e.clientX, e.clientY);
                        if (point) {
                            markMultiPlans = new MarkMultiPlans(events);
                            markMultiPlans.drawStart(point);
                            fire(GetScene).add(markMultiPlans);
                        }
                    } else {
                        // 继续
                        if (mouseState.lastMovePoint && fire(RaycasterRayDistanceToPoint, e.clientX, e.clientY, mouseState.lastMovePoint) < 0.03) {
                            // 点击位置与移动提示点相近，按选中提示点处理
                            markMultiPlans.drawUpdate(null, true, mouseState.lastMovePoint, true);
                            mouseState.lastClickPointTime = Date.now();
                        } else {
                            // 按点击位置计算选点
                            const point: Vector3 = await fire(SelectMarkPoint, e.clientX, e.clientY);
                            if (point) {
                                markMultiPlans.drawUpdate(null, true, point, true);
                                mouseState.lastClickPointTime = Date.now();
                            } else {
                                mouseState.lastClickPointTime = 0;
                            }
                        }
                    }
                } else if (opts.markType === 'circle') {
                    if (!markCirclePlan) {
                        // 开始
                        const point: Vector3 = await fire(SelectMarkPoint, e.clientX, e.clientY);
                        if (point) {
                            markCirclePlan = new MarkCirclePlan(events);
                            markCirclePlan.drawStart(point);
                            fire(GetScene).add(markCirclePlan);
                        }
                    } else {
                        // 完成
                        const point: Vector3 = await fire(SelectMarkPoint, e.clientX, e.clientY);
                        if (point) {
                            markCirclePlan.drawFinish(point);
                            markCirclePlan = null;
                        } else {
                            mouseState.isDbClick && fire(CancelCurrentMark); // 取消标记
                        }
                    }
                }

                mouseState.lastClickX = e.clientX;
                mouseState.lastClickY = e.clientY;
            }
        }

        // 右击不移动时，调整旋转中心，但在第一人称模式下忽略
        if (mouseState.down === 2 && !mouseState.move) {
            const useCustomControl = opts.useCustomControl === true;

            if (!useCustomControl) {
                console.log('[MouseUp] 非FPS模式下右键点击，调整相机目标点');
                fire(SelectPointAndLookAt, e.clientX, e.clientY);
            } else {
                console.log('[MouseUp] FPS模式下忽略右键点击');
            }
        }

        mouseState.down = 0;
        mouseState.move = false;
        lastActionTome = Date.now();
    };

    function canvasTouchstartEventListener(event: TouchEvent) {
        event.preventDefault();
        if (disposed) return;
        fire(StopAutoRotate);

        // 获取触摸点数量
        mouseState.down = event.touches.length;

        // 获取选项，判断是否启用自定义控制
        const opts: Reall3dViewerOptions = fire(GetOptions);
        const useCustomControl = opts.useCustomControl === true;

        if (mouseState.down === 1) {
            // 单指触摸
            mouseState.move = false;
            mouseState.x = event.touches[0].clientX;
            mouseState.y = event.touches[0].clientY;

            // 在自定义模式下初始化欧拉角
            if (useCustomControl) {
                // 初始化欧拉角（如果是第一次进入FPS模式）
                if (Math.abs(fpsPitch) < 0.001 && Math.abs(fpsYaw) < 0.001) {
                    console.log('[TouchStart] 初始化欧拉角');
                    initFPSEulerAngles();
                }

                // 禁用控制器
                const controls = fire(GetControls);
                controls.enabled = false;
            }
        } else if (mouseState.down === 2 && useCustomControl) {
            // 双指触摸 - 在自定义控制模式下记录初始位置和距离
            const touch1 = event.touches[0];
            const touch2 = event.touches[1];

            // 记录初始触摸点位置
            mouseState.touchStartX1 = touch1.clientX;
            mouseState.touchStartY1 = touch1.clientY;
            mouseState.touchStartX2 = touch2.clientX;
            mouseState.touchStartY2 = touch2.clientY;

            // 记录上一帧触摸点位置（初始时与起始位置相同）
            mouseState.touchPrevX1 = touch1.clientX;
            mouseState.touchPrevY1 = touch1.clientY;
            mouseState.touchPrevX2 = touch2.clientX;
            mouseState.touchPrevY2 = touch2.clientY;

            // 计算两指之间的距离
            const dx = touch1.clientX - touch2.clientX;
            const dy = touch1.clientY - touch2.clientY;
            mouseState.touchStartDistance = Math.sqrt(dx * dx + dy * dy);
            mouseState.touchPrevDistance = mouseState.touchStartDistance;

            console.log('[TouchStart] 双指触摸开始, 初始距离:', mouseState.touchStartDistance);

            // 在自定义模式下禁用控制器，以便我们完全控制相机
            const controls = fire(GetControls);
            controls.enabled = false;

            // 确保OrbitControls不会在帧更新时修改相机位置
            controls.minDistance = controls.maxDistance = controls.getDistance();
        }

        lastActionTome = Date.now();
    }

    function canvasTouchmoveEventListener(event: TouchEvent) {
        event.preventDefault();
        if (disposed) return;

        // 获取选项，判断是否启用自定义控制
        const opts: Reall3dViewerOptions = fire(GetOptions);
        const useCustomControl = opts.useCustomControl === true;
        console.log('[TouchMove] 触摸移动', event.touches.length, useCustomControl);

        if (event.touches.length === 1) {
            // 单指移动
            mouseState.move = true;

            // 自定义模式下单指控制视角
            if (useCustomControl) {
                const touch = event.touches[0];

                // 计算移动距离
                const deltaX = touch.clientX - mouseState.x;
                const deltaY = touch.clientY - mouseState.y;

                // 保存相机当前位置
                const camera = fire(GetCamera);
                const controls = fire(GetControls);
                const originalPosition = camera.position.clone();

                // 旋转速度因子（与鼠标拖拽类似但灵敏度调整）
                const rotateFactorX = 0.001; // 水平旋转灵敏度
                const rotateFactorY = 0.001; // 垂直旋转灵敏度

                console.log('[TouchMove] 单指模式视角控制，移动距离:', deltaX, deltaY);

                // 水平方向移动控制左右旋转
                if (Math.abs(deltaX) > 0) {
                    if (deltaX < 0) {
                        // 向左划 - 向左旋转
                        fire(CameraRotateLeftFPS, Math.abs(deltaX) * rotateFactorX);
                    } else {
                        // 向右划 - 向右旋转
                        fire(CameraRotateRightFPS, Math.abs(deltaX) * rotateFactorX);
                    }
                }

                // 垂直方向移动控制上下旋转
                if (Math.abs(deltaY) > 0) {
                    if (deltaY < 0) {
                        // 向上划 - 向上看（抬头）
                        fire(CameraRotateDownFPS, Math.abs(deltaY) * rotateFactorY);
                    } else {
                        // 向下划 - 向下看（低头）
                        fire(CameraRotateUpFPS, Math.abs(deltaY) * rotateFactorY);
                    }
                }

                // 确保相机位置恢复到原始位置
                camera.position.copy(originalPosition);

                // 确保距离限制设置正确，防止OrbitControls自动调整距离
                controls.minDistance = controls.maxDistance = controls.getDistance();

                // 更新鼠标位置
                mouseState.x = touch.clientX;
                mouseState.y = touch.clientY;

                // 强制更新场景
                fire(ViewerNeedUpdate);
            }
        } else if (event.touches.length === 2 && useCustomControl) {
            console.warn('[TouchMove] 双指移动 - 在自定义控制模式下处理');
            // 双指移动 - 在自定义控制模式下处理
            const touch1 = event.touches[0];
            const touch2 = event.touches[1];

            // 计算当前两指距离
            const dx = touch1.clientX - touch2.clientX;
            const dy = touch1.clientY - touch2.clientY;
            const currentDistance = Math.sqrt(dx * dx + dy * dy);

            // 计算距离变化（正值表示两指分开，负值表示两指靠拢）
            const distanceDelta = currentDistance - mouseState.touchPrevDistance;

            // 计算中点位置
            const currentMidX = (touch1.clientX + touch2.clientX) / 2;
            const currentMidY = (touch1.clientY + touch2.clientY) / 2;
            const prevMidX = (mouseState.touchPrevX1 + mouseState.touchPrevX2) / 2;
            const prevMidY = (mouseState.touchPrevY1 + mouseState.touchPrevY2) / 2;

            // 计算中点移动量
            const midDeltaX = currentMidX - prevMidX;
            const midDeltaY = currentMidY - prevMidY;

            // 速度因子
            const moveSpeed = opts.cameraMoveSpeed !== undefined ? opts.cameraMoveSpeed : 0.05;
            const touchFactor = 0.1; // 触摸移动灵敏度因子

            // 判断主要手势类型 - 改进手势识别逻辑
            const absDistanceDelta = Math.abs(distanceDelta);
            const absMidDeltaX = Math.abs(midDeltaX);
            const absMidDeltaY = Math.abs(midDeltaY);

            // 设置手势识别阈值
            const zoomThreshold = 4; // 缩放手势阈值
            const panThreshold = 2; // 平移手势阈值

            // 优先识别缩放手势（距离变化比平移变化大）
            const isZoomGesture = absDistanceDelta > zoomThreshold && absDistanceDelta > (absMidDeltaX + absMidDeltaY) * 0.5;
            const isPanGesture = !isZoomGesture && (absMidDeltaX > panThreshold || absMidDeltaY > panThreshold);

            if (isZoomGesture) {
                // 双指开合控制前后移动 - 使用更稳定的缩放逻辑
                const camera = fire(GetCamera) as PerspectiveCamera;
                const controls = fire(GetControls);

                // 计算缩放系数，使用更平滑的算法
                const scaleFactor = 0.003; // 进一步降低灵敏度
                const zoomScale = distanceDelta > 0 ? (1 - absDistanceDelta * scaleFactor) : (1 + absDistanceDelta * scaleFactor);

                // 获取当前相机到目标的方向向量
                const direction = camera.position.clone().sub(controls.target).normalize();

                // 计算当前距离
                const currentDistance = camera.position.distanceTo(controls.target);

                // 计算新的距离，并限制在合理范围内
                const newDistance = Math.max(0.1, Math.min(1000, currentDistance * zoomScale));

                // 只有当距离变化足够大时才更新
                if (Math.abs(newDistance - currentDistance) > 0.01) {
                    // 计算新的相机位置
                    const newPosition = controls.target.clone().add(direction.multiplyScalar(newDistance));

                    // 更新相机位置
                    camera.position.copy(newPosition);

                    // 确保控制器距离限制正确设置
                    controls.minDistance = controls.maxDistance = newDistance;

                    console.log('[TouchMove] 双指缩放, 距离变化:', currentDistance.toFixed(2), '->', newDistance.toFixed(2));
                }
            } else if (isPanGesture) {
                // 双指平移控制左右和上下移动
                // 水平移动 - 左右移动
                if (absMidDeltaX > absMidDeltaY && absMidDeltaX > panThreshold) {
                    const scaledSpeed = absMidDeltaX * touchFactor * moveSpeed;

                    if (midDeltaX < 0) {
                        // 向右移动
                        fire(CameraMoveRight, scaledSpeed);
                        console.log('[TouchMove] 双指右移 - 向右移动, 速度:', scaledSpeed);
                    } else {
                        // 向左移动
                        fire(CameraMoveLeft, scaledSpeed);
                        console.log('[TouchMove] 双指左移 - 向左移动, 速度:', scaledSpeed);
                    }
                }
                // 垂直移动 - 上下移动
                else if (absMidDeltaY > panThreshold) {
                    const scaledSpeed = absMidDeltaY * touchFactor * moveSpeed;

                    if (midDeltaY < 0) {
                        // 向下移动
                        fire(CameraMoveDown, scaledSpeed);
                        console.log('[TouchMove] 双指下移 - 向下移动, 速度:', scaledSpeed);
                    } else {
                        // 向上移动
                        fire(CameraMoveUp, scaledSpeed);
                        console.log('[TouchMove] 双指上移 - 向上移动, 速度:', scaledSpeed);
                    }
                }
            }

            // 更新上一帧的触摸点位置和距离
            mouseState.touchPrevX1 = touch1.clientX;
            mouseState.touchPrevY1 = touch1.clientY;
            mouseState.touchPrevX2 = touch2.clientX;
            mouseState.touchPrevY2 = touch2.clientY;
            mouseState.touchPrevDistance = currentDistance;

            // 强制更新场景
            fire(ViewerNeedUpdate);
        }

        lastActionTome = Date.now();
    }

    function canvasTouchendEventListener(event: TouchEvent) {
        if (disposed) return;

        // 获取选项，判断是否启用自定义控制
        const opts: Reall3dViewerOptions = fire(GetOptions);
        const useCustomControl = opts.useCustomControl === true;

        // 恢复控制器状态
        if (useCustomControl) {
            const controls = fire(GetControls);
            controls.enabled = false; // 在自定义模式下保持禁用状态
        }

        // 单指点击选择点（非自定义模式）- 已注释移除移动端单指点击选中target的交互
        // if (mouseState.down === 1 && !mouseState.move) {
        //     // 只有在非FPS模式下才调整相机目标点
        //     if (!useCustomControl) {
        //         fire(SelectPointAndLookAt, mouseState.x, mouseState.y);
        //     } else {
        //         console.log('[TouchEnd] FPS模式下忽略点击选择');
        //     }
        // }

        // 重置触摸状态
        if (event.touches.length === 0) {
            mouseState.down = 0;
            mouseState.move = false;
        } else {
            mouseState.down = event.touches.length;
        }

        lastActionTome = Date.now();
    }

    window.addEventListener('keydown', keydownEventListener);
    window.addEventListener('keyup', keyupEventListener);
    window.addEventListener('blur', blurEventListener);
    window.addEventListener('wheel', wheelEventListener, { passive: false });
    canvas.addEventListener('contextmenu', canvasContextmenuEventListener);
    canvas.addEventListener('mousedown', canvasMousedownEventListener);
    canvas.addEventListener('mousemove', canvasMousemoveEventListener);
    canvas.addEventListener('mouseup', canvasMouseupEventListener);
    canvas.addEventListener('touchstart', canvasTouchstartEventListener, { passive: false });
    canvas.addEventListener('touchmove', canvasTouchmoveEventListener, { passive: false });
    canvas.addEventListener('touchend', canvasTouchendEventListener, { passive: false });

    window.addEventListener('resize', resize);
    resize();
    function resize() {
        const { width, height, top, left } = fire(GetCanvasSize);
        const camera: PerspectiveCamera = fire(GetCamera);
        camera.aspect = width / height;
        camera.updateProjectionMatrix();
        const cSS3DRenderer: CSS3DRenderer = fire(GetCSS3DRenderer);
        cSS3DRenderer.setSize(width, height);
        cSS3DRenderer.domElement.style.position = 'absolute';
        cSS3DRenderer.domElement.style.left = `${left}px`;
        cSS3DRenderer.domElement.style.top = `${top}px`;
        const renderer = fire(GetRenderer);
        renderer.setPixelRatio(Math.min(devicePixelRatio, 2));
        renderer.setSize(width, height);
    }

    on(EventListenerDispose, () => {
        disposed = true;
        window.removeEventListener('keydown', keydownEventListener);
        window.removeEventListener('keyup', keyupEventListener);
        window.removeEventListener('blur', blurEventListener);
        window.removeEventListener('wheel', wheelEventListener);
        canvas.removeEventListener('contextmenu', canvasContextmenuEventListener);
        canvas.removeEventListener('mousedown', canvasMousedownEventListener);
        canvas.removeEventListener('mousemove', canvasMousemoveEventListener);
        canvas.removeEventListener('mouseup', canvasMouseupEventListener);
        canvas.removeEventListener('touchstart', canvasTouchstartEventListener);
        canvas.removeEventListener('touchmove', canvasTouchmoveEventListener);
        canvas.removeEventListener('touchend', canvasTouchendEventListener);
        window.removeEventListener('resize', resize);
    });
}

// 使用当前相机的up向量定义水平面
function moveCameraHorizontal(controls, direction, step = 0.012) {
    // 使用相机的up向量作为平面法线
    const planeNormal = controls.object.up.clone().normalize();

    // 将方向向量投影到以up向量为法线的平面上
    const projectedDirection = new Vector3().copy(direction).projectOnPlane(planeNormal).normalize();

    const moveVector = projectedDirection.multiplyScalar(step);
    controls.object.position.add(moveVector);
    controls.target.add(moveVector);
    controls.update();
}
// 左移
function moveLeft(controls, step = 0.2) {
    const direction = new Vector3();
    controls.object.getWorldDirection(direction);
    const leftDir = new Vector3().crossVectors(controls.object.up, direction).normalize();

    moveCameraHorizontal(controls, leftDir, step);
}
// 右移
function moveRight(controls, step = 0.2) {
    const direction = new Vector3();
    controls.object.getWorldDirection(direction);
    const rightDir = new Vector3().crossVectors(controls.object.up, direction).normalize().negate();

    moveCameraHorizontal(controls, rightDir, step);
}
// 前进
function moveForward(controls, step = 0.2) {
    const forwardDir = new Vector3();
    controls.object.getWorldDirection(forwardDir);
    moveCameraHorizontal(controls, forwardDir, step);
}
// 后退
function moveBackward(controls, step = 0.2) {
    const backwardDir = new Vector3();
    controls.object.getWorldDirection(backwardDir).negate();
    moveCameraHorizontal(controls, backwardDir, step);
}

/**
 * 相机位置不变，目标点顺时针转动（像人转动头部）
 * @param {OrbitControls} controls - 轨道控制器
 * @param {number} angle - 转动角度（弧度制）
 */
function rotateTargetClockwise(controls, angle = 0.006) {
    // 获取相机当前位置和目标点位置
    const cameraPosition = controls.object.position.clone();
    const targetPosition = controls.target.clone();

    // 计算相机到目标的向量
    const direction = new Vector3().subVectors(targetPosition, cameraPosition);

    // 获取相机的上方向（旋转轴）
    const upVector = controls.object.up.clone().normalize();

    // 创建四元数表示顺时针旋转
    const quaternion = new Quaternion();
    quaternion.setFromAxisAngle(upVector, -angle); // 负角度表示顺时针

    // 应用旋转到方向向量
    direction.applyQuaternion(quaternion);

    // 计算新的目标点位置
    const newTargetPosition = new Vector3().addVectors(cameraPosition, direction);

    // 更新目标点
    controls.target.copy(newTargetPosition);
    controls.update();
}
// 左看
function rotateTargetLeft(controls) {
    rotateTargetClockwise(controls, -0.01);
}
// 右看
function rotateTargetRight(controls) {
    rotateTargetClockwise(controls, 0.01);
}

/**
 * 相机位置不变，目标点上下转动（像人抬头低头）
 * @param {OrbitControls} controls - 轨道控制器
 * @param {number} angle - 转动角度（弧度制），正数为抬头，负数为低头
 */
function rotateTargetVertical(controls, angle = 0.006) {
    // 获取相机当前位置和目标点位置
    const cameraPosition = controls.object.position.clone();
    const targetPosition = controls.target.clone();

    // 计算相机到目标的向量
    const direction = new Vector3().subVectors(targetPosition, cameraPosition);

    // 获取相机的右侧方向（用于垂直旋转的轴）
    const forwardDir = new Vector3();
    controls.object.getWorldDirection(forwardDir);
    const rightVector = new Vector3().crossVectors(controls.object.up, forwardDir).normalize();

    // 创建四元数表示垂直旋转
    const quaternion = new Quaternion();
    quaternion.setFromAxisAngle(rightVector, angle);

    // 应用旋转到方向向量
    direction.applyQuaternion(quaternion);

    // 计算新的目标点位置
    const newTargetPosition = new Vector3().addVectors(cameraPosition, direction);

    // 更新目标点
    controls.target.copy(newTargetPosition);
    controls.update();
}

// 抬头
function rotateTargetUp(controls, angle = 0.01) {
    rotateTargetVertical(controls, -angle);
}

// 低头
function rotateTargetDown(controls, angle = 0.01) {
    rotateTargetVertical(controls, angle);
}
